@echo off
echo ========================================
echo    启动响应式布局项目
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Python未安装或未添加到PATH环境变量
    echo 请先安装Python: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] 检测到Python已安装
echo [信息] 启动本地服务器...
echo [信息] 服务器地址: http://localhost:8000
echo [信息] 按 Ctrl+C 停止服务器
echo [信息] 正在打开浏览器...
echo.

:: 自动打开浏览器
start http://localhost:8000

:: 启动服务器
python -m http.server 8000

pause
