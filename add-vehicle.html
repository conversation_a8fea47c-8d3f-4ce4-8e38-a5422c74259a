<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add a Vehicle</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <ul class="nav-links">
                <li><a href="index.html">People search</a></li>
                <li><a href="vehicle.html">Vehicle search</a></li>
                <li><a href="add-vehicle.html">Add a vehicle</a></li>
            </ul>
        </nav>
    </header>

    <!-- Page Container -->
    <div class="page-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <img src="add-vehicle.jpg" alt="Vehicle registration illustration">
        </aside>

        <!-- Main Content -->
        <main id="container">
            <h1>Add a Vehicle</h1>
            <form id="addVehicleForm">
                <label for="rego">Registration Number:</label>
                <input type="text" id="rego" required>
                <label for="make">Make:</label>
                <input type="text" id="make" required>
                <label for="model">Model:</label>
                <input type="text" id="model" required>
                <label for="colour">Colour:</label>
                <input type="text" id="colour" required>
                <label for="owner">Owner Name:</label>
                <input type="text" id="owner" required>
                <button type="button" id="checkOwner">Check owner</button>
            </form>
            <div id="owner-results"></div>
            <div id="new-owner-button-container"></div>
            <div id="new-owner-form" style="display: none;">
                <h2>Add New Owner</h2>
                <form>
                    <label for="new-name">Name:</label>
                    <input type="text" id="new-name" required>
                    <label for="new-address">Address:</label>
                    <input type="text" id="new-address" required>
                    <label for="new-dob">Date of Birth:</label>
                    <input type="date" id="new-dob" required>
                    <label for="new-license">License Number:</label>
                    <input type="text" id="new-license" aria-describedby="license-help" required>
                    <small id="license-help">Enter the license number exactly as shown on ID.</small>
                    <label for="new-expire">Expiry Date:</label>
                    <input type="date" id="new-expire" required>
                    <button type="button" id="addOwner">Add owner</button>
                </form>
            </div>
            <div id="message-owner"></div>
            <div id="message-vehicle"></div>
            <button type="button" id="addVehicle">Add vehicle</button>
        </main>
    </div>

    <!-- Footer -->
    <footer>
        <p>COMP1004 Coursework © 2024</p>
    </footer>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="app.js"></script>
</body>
</html>
