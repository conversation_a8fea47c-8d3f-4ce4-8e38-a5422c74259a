<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Supabase Connection Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <ul class="nav-links">
                <li><a href="index.html">People search</a></li>
                <li><a href="vehicle.html">Vehicle search</a></li>
                <li><a href="add-vehicle.html">Add a vehicle</a></li>
                <li><a href="test-connection.html">Test Connection</a></li>
            </ul>
        </nav>
    </header>

    <!-- Page Container -->
    <div class="page-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3>Connection Status</h3>
            <div id="connection-status">Testing...</div>
        </aside>

        <!-- Main Content -->
        <main id="container">
            <h1>Supabase Connection Test</h1>
            
            <div class="test-section">
                <h2>Database Tables</h2>
                <button id="test-people" type="button">Test People Table</button>
                <button id="test-vehicles" type="button">Test Vehicles Table</button>
                <button id="test-insert" type="button">Test Insert (Safe)</button>
            </div>

            <div id="test-results">
                <h3>Test Results:</h3>
                <div id="results-content"></div>
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer>
        <p>COMP1004 Coursework © 2024 - Connection Test</p>
    </footer>

    <script type="module">
        import { createClient } from '@supabase/supabase-js';

        const supabase = createClient('https://qncgwnrexkjfcwiktbca.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFuY2d3bnJleGtqZmN3aWt0YmNhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxODA3NzIsImV4cCI6MjA2Mjc1Njc3Mn0.LVpJZRNxAnqw3jgtRvWviQanV-hHLmSuTrAa993Dy80');

        function showResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('results-content');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = isSuccess ? 'success' : 'error';
            resultsDiv.innerHTML += `<div class="${statusClass}">[${timestamp}] ${message}</div>`;
        }

        function updateConnectionStatus(status, isConnected = true) {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.textContent = status;
            statusDiv.className = isConnected ? 'success' : 'error';
        }

        // Test People table
        document.getElementById('test-people').addEventListener('click', async () => {
            try {
                const { data, error } = await supabase.from('People').select('*').limit(5);
                if (error) throw error;
                showResult(`✅ People table connected successfully. Found ${data.length} records.`);
                if (data.length > 0) {
                    showResult(`Sample record: ${JSON.stringify(data[0], null, 2)}`);
                }
            } catch (err) {
                showResult(`❌ People table error: ${err.message}`, false);
            }
        });

        // Test Vehicles table
        document.getElementById('test-vehicles').addEventListener('click', async () => {
            try {
                const { data, error } = await supabase.from('Vehicles').select('*').limit(5);
                if (error) throw error;
                showResult(`✅ Vehicles table connected successfully. Found ${data.length} records.`);
                if (data.length > 0) {
                    showResult(`Sample record: ${JSON.stringify(data[0], null, 2)}`);
                }
            } catch (err) {
                showResult(`❌ Vehicles table error: ${err.message}`, false);
            }
        });

        // Test basic connection
        async function testConnection() {
            try {
                const { data, error } = await supabase.from('People').select('count', { count: 'exact' });
                if (error) throw error;
                updateConnectionStatus('✅ Connected to Supabase', true);
                showResult('✅ Initial connection test successful');
            } catch (err) {
                updateConnectionStatus('❌ Connection Failed', false);
                showResult(`❌ Connection error: ${err.message}`, false);
            }
        }

        // Run initial connection test
        testConnection();
    </script>
</body>
</html>
