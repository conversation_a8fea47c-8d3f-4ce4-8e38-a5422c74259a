<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Debug People Table</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Debug People Table</h1>
    
    <button onclick="checkTableStructure()">Check Table Structure</button>
    <button onclick="testInsert()">Test Insert</button>
    <button onclick="viewExistingData()">View Existing Data</button>
    
    <div id="results"></div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        const { createClient } = supabase;
        
        const supabaseClient = createClient(
            'https://qncgwnrexkjfcwiktbca.supabase.co', 
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFuY2d3bnJleGtqZmN3aWt0YmNhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxODA3NzIsImV4cCI6MjA2Mjc1Njc3Mn0.LVpJZRNxAnqw3jgtRvWviQanV-hHLmSuTrAa993Dy80'
        );

        function showResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = isSuccess ? 'success' : 'error';
            resultsDiv.innerHTML += `<div class="result ${statusClass}">[${timestamp}] ${message}</div>`;
        }

        async function checkTableStructure() {
            try {
                showResult('🔍 Checking People table structure...');
                
                // 尝试获取表的一些记录来了解结构
                const { data, error } = await supabaseClient
                    .from('People')
                    .select('*')
                    .limit(1);
                
                if (error) {
                    throw error;
                }
                
                if (data && data.length > 0) {
                    showResult('✅ Table structure (sample record):', true);
                    showResult(`<pre>${JSON.stringify(data[0], null, 2)}</pre>`, true);
                } else {
                    showResult('ℹ️ Table exists but is empty', true);
                }
                
            } catch (err) {
                showResult(`❌ Error checking table: ${err.message}`, false);
                console.error('Table check error:', err);
            }
        }

        async function testInsert() {
            try {
                showResult('🧪 Testing insert operation...');
                
                const testOwner = {
                    Name: 'Test User ' + Date.now(),
                    Address: 'Test Address',
                    DOB: '1990-01-01',
                    LicenseNumber: 'TEST' + Date.now(),
                    ExpiryDate: '2025-12-31'
                };
                
                showResult(`📤 Attempting to insert: <pre>${JSON.stringify(testOwner, null, 2)}</pre>`, true);
                
                const { data: insertedOwner, error: insertError } = await supabaseClient
                    .from('People')
                    .insert([testOwner])
                    .select('PersonID, Name');
                
                if (insertError) {
                    throw insertError;
                }
                
                showResult('✅ Insert successful!', true);
                showResult(`📥 Returned data: <pre>${JSON.stringify(insertedOwner, null, 2)}</pre>`, true);
                
            } catch (err) {
                showResult(`❌ Insert failed: ${err.message}`, false);
                console.error('Insert error:', err);
                
                // 显示详细错误信息
                if (err.details) {
                    showResult(`🔍 Error details: ${err.details}`, false);
                }
                if (err.hint) {
                    showResult(`💡 Hint: ${err.hint}`, false);
                }
            }
        }

        async function viewExistingData() {
            try {
                showResult('📋 Viewing existing People data...');
                
                const { data, error } = await supabaseClient
                    .from('People')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                
                showResult(`✅ Found ${data.length} records:`, true);
                data.forEach((person, index) => {
                    showResult(`Record ${index + 1}: <pre>${JSON.stringify(person, null, 2)}</pre>`, true);
                });
                
            } catch (err) {
                showResult(`❌ Error viewing data: ${err.message}`, false);
            }
        }

        // 页面加载时自动检查表结构
        window.onload = function() {
            checkTableStructure();
        };
    </script>
</body>
</html>
